defmodule Drops.Relations.StructSpec do
  use Drops.RelationCase, async: false

  describe "loading custom struct" do
    @tag relations: [:users]

    test "Ecto.Repo can load our custom struct", %{users: users} do
      user = users.first()

      assert users.ecto_schema() == user.__schema__()

      assert user.__attributes__[:id] == user.id
      assert user.__attributes__[:name] == user.name
    end
  end
end
