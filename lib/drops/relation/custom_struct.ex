defmodule Drops.Relation.CustomStruct do
  @moduledoc """
  A custom struct that can work with Ecto.Repo without using `use Ecto.Schema`.

  This module provides the minimal interface required by Ecto.Repo to load and work
  with database records. It implements the necessary __schema__/1 and __schema__/2
  functions that <PERSON>ct<PERSON> expects from schema modules.

  The struct has three main attributes:
  - `:__schema__` - The Ecto schema module that defines the structure
  - `:__attributes__` - A map containing the actual data loaded from the database
  - Dynamic field accessors that delegate to the __attributes__ map

  ## Usage

      # Create a custom struct with a schema and attributes
      schema_module = MyApp.UserSchema  # An Ecto.Schema module
      attributes = %{id: 1, name: "<PERSON>", email: "<EMAIL>"}

      custom_struct = Drops.Relation.CustomStruct.new(schema_module, attributes)

      # Access fields dynamically
      custom_struct.id    # => 1
      custom_struct.name  # => "John"

      # Access the underlying data
      custom_struct.__attributes__[:id]  # => 1

      # Get the schema module
      custom_struct.__schema__()  # => MyApp.UserSchema
  """

  @doc """
  Creates a new custom struct with the given schema module and attributes.

  ## Parameters

  - `schema_module` - An Ecto.Schema module that defines the structure
  - `attributes` - A map of field names to values

  ## Returns

  A new custom struct that can be used with Ecto.Repo functions.
  """
  def new(schema_module, attributes \\ %{}) do
    # Create the factory module and then create an instance
    factory = create_factory(schema_module)
    factory.new(attributes)
  end

  @doc """
  Creates a dynamic struct module that implements the Ecto.Schema interface.

  This function generates a module at runtime that:
  1. Stores the schema module and attributes
  2. Implements __schema__/1 and __schema__/2 functions by delegating to the schema module
  3. Provides dynamic field access that delegates to the __attributes__ map
  4. Can be used with Ecto.Repo functions
  """
  def create_dynamic_struct(schema_module, attributes) do
    # Ensure the schema module is loaded and is an Ecto schema
    unless Code.ensure_loaded?(schema_module) and
             function_exported?(schema_module, :__schema__, 1) do
      raise ArgumentError, "Expected Ecto schema module, got: #{inspect(schema_module)}"
    end

    # Get the fields from the schema to create accessors
    fields = schema_module.__schema__(:fields)

    # Create a unique module name for this struct instance
    module_name =
      Module.concat([
        Drops.Relation.CustomStruct,
        "Instance#{:erlang.unique_integer([:positive])}"
      ])

    # Generate the module AST
    module_ast =
      quote do
        defmodule unquote(module_name) do
          @schema_module unquote(schema_module)
          @attributes unquote(Macro.escape(attributes))

          # Define the struct with the three main attributes
          defstruct [:__schema__, :__attributes__] ++ unquote(fields)

          # Implement the Ecto.Schema interface by delegating to the schema module
          def __schema__(query) do
            @schema_module.__schema__(query)
          end

          def __schema__(query, field) do
            @schema_module.__schema__(query, field)
          end

          # Create a new instance of this struct
          def new(attrs \\ %{}) do
            # Merge provided attributes with defaults
            merged_attrs = Map.merge(@attributes, attrs)

            # Create the struct with schema and attributes
            struct_data = %{
              __schema__: @schema_module,
              __attributes__: merged_attrs
            }

            # Add field values directly to the struct for easy access
            field_data = Map.take(merged_attrs, unquote(fields))

            struct(__MODULE__, Map.merge(struct_data, field_data))
          end

          # Generate field accessors that delegate to __attributes__
          unquote_splicing(generate_field_accessors(fields))
        end
      end

    # Compile and load the module
    {_result, _bytecode} = Code.eval_quoted(module_ast)

    # Create and return an instance
    module_name.new(attributes)
  end

  # Generate field accessor functions
  defp generate_field_accessors(_fields) do
    # Since we're setting the field values directly in the struct,
    # we don't need to generate accessor functions
    # The struct fields themselves handle access via pattern matching
    []
  end

  @doc """
  Creates a custom struct factory that can be used to create multiple instances
  with the same schema but different attributes.

  This is useful when you want to create many structs with the same schema
  without regenerating the module each time.
  """
  def create_factory(schema_module) do
    # Ensure the schema module is loaded and is an Ecto schema
    unless Code.ensure_loaded?(schema_module) and
             function_exported?(schema_module, :__schema__, 1) do
      raise ArgumentError, "Expected Ecto schema module, got: #{inspect(schema_module)}"
    end

    # Get the fields from the schema
    fields = schema_module.__schema__(:fields)

    # Create a factory module name
    factory_name =
      Module.concat([
        Drops.Relation.CustomStruct,
        "Factory#{:erlang.unique_integer([:positive])}"
      ])

    # Generate the factory module
    factory_ast =
      quote do
        defmodule unquote(factory_name) do
          @schema_module unquote(schema_module)
          @fields unquote(fields)

          # Define the struct - include __schema__, __attributes__ and the actual fields
          defstruct [:__schema__, :__attributes__] ++ @fields

          # Implement the Ecto.Schema interface by delegating to the schema module
          def __schema__(query) do
            @schema_module.__schema__(query)
          end

          def __schema__(query, field) do
            @schema_module.__schema__(query, field)
          end

          # Create new instances
          def new(attributes \\ %{}) do
            # Create the struct with schema and attributes
            struct_data = %{
              __schema__: @schema_module,
              __attributes__: attributes
            }

            # Add field values directly to the struct for easy access
            field_data = Map.take(attributes, @fields)

            struct(__MODULE__, Map.merge(struct_data, field_data))
          end

          # Generate field accessors
          unquote_splicing(generate_field_accessors(fields))
        end
      end

    # Compile and load the factory module
    {_result, _bytecode} = Code.eval_quoted(factory_ast)

    factory_name
  end
end
